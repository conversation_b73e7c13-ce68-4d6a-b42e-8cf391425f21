"use client"

import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight, Moon, Sun, User } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "@/components/theme-provider"
import { BreadcrumbItem } from "@/types/menu"
import { menuConfig } from "@/config/menu"

interface HeaderProps {
  /** 自定义类名 */
  className?: string
}

/**
 * 根据路径生成面包屑导航
 */
const generateBreadcrumbs = (pathname: string): BreadcrumbItem[] => {
  const segments = pathname.split("/").filter(<PERSON><PERSON>an)
  const breadcrumbs: BreadcrumbItem[] = [
    { title: "首页", href: "/" }
  ]

  // 递归查找菜单项
  const findMenuItem = (items: any[], path: string): any => {
    for (const item of items) {
      if (item.href === path) {
        return item
      }
      if (item.children) {
        const found = findMenuItem(item.children, path)
        if (found) return found
      }
    }
    return null
  }

  // 构建面包屑路径
  let currentPath = ""
  for (let i = 0; i < segments.length; i++) {
    currentPath += "/" + segments[i]
    const menuItem = findMenuItem(menuConfig, currentPath)
    
    if (menuItem) {
      breadcrumbs.push({
        title: menuItem.title,
        href: currentPath,
        isCurrentPage: i === segments.length - 1
      })
    } else {
      // 如果找不到对应的菜单项，使用路径段作为标题
      const title = segments[i].charAt(0).toUpperCase() + segments[i].slice(1)
      breadcrumbs.push({
        title,
        href: currentPath,
        isCurrentPage: i === segments.length - 1
      })
    }
  }

  return breadcrumbs
}

/**
 * 面包屑导航组件
 */
const Breadcrumb: React.FC<{ items: BreadcrumbItem[] }> = ({ items }) => {
  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
      {items.map((item, index) => (
        <React.Fragment key={item.href || index}>
          {index > 0 && <ChevronRight className="h-4 w-4" />}
          {item.isCurrentPage ? (
            <span className="font-medium text-foreground">{item.title}</span>
          ) : (
            <Link
              href={item.href || "#"}
              className="hover:text-foreground transition-colors"
            >
              {item.title}
            </Link>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}

/**
 * 主题切换组件
 */
const ThemeToggle: React.FC = () => {
  const { theme, setTheme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-9 w-9">
          <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">切换主题</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          <Sun className="mr-2 h-4 w-4" />
          <span>浅色</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          <Moon className="mr-2 h-4 w-4" />
          <span>深色</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          <span className="mr-2 h-4 w-4">💻</span>
          <span>跟随系统</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * 用户菜单组件
 */
const UserMenu: React.FC = () => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-9 w-9 rounded-full">
          <Avatar className="h-9 w-9">
            <AvatarImage src="/avatars/01.png" alt="用户头像" />
            <AvatarFallback>
              <User className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">演示用户</p>
            <p className="text-xs leading-none text-muted-foreground">
              <EMAIL>
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4" />
          <span>个人资料</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <span className="mr-2 h-4 w-4">⚙️</span>
          <span>设置</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <span className="mr-2 h-4 w-4">🚪</span>
          <span>退出登录</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * 头部组件
 * 包含面包屑导航、主题切换和用户菜单
 */
export const Header: React.FC<HeaderProps> = ({ className }) => {
  const pathname = usePathname()
  const breadcrumbs = generateBreadcrumbs(pathname)

  return (
    <header
      className={cn(
        "sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
        className
      )}
    >
      <div className="container flex h-14 items-center justify-between px-4">
        {/* 面包屑导航 */}
        <div className="flex items-center space-x-4">
          <Breadcrumb items={breadcrumbs} />
        </div>

        {/* 右侧操作区 */}
        <div className="flex items-center space-x-2">
          <ThemeToggle />
          <UserMenu />
        </div>
      </div>
    </header>
  )
}
