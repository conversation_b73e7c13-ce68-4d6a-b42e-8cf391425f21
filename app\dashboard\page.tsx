import React from "react"
import { BarChart3, Users, ShoppingCart, TrendingUp } from "lucide-react"

/**
 * 统计卡片组件
 */
interface StatCardProps {
  title: string
  value: string
  change: string
  icon: React.ReactNode
  trend: "up" | "down"
}

const StatCard: React.FC<StatCardProps> = ({ title, value, change, icon, trend }) => {
  return (
    <div className="bg-card rounded-lg border p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          <p className={`text-xs ${trend === "up" ? "text-green-600" : "text-red-600"}`}>
            {change}
          </p>
        </div>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </div>
    </div>
  )
}

/**
 * 仪表板页面
 * 显示概览统计信息和图表
 */
export default function DashboardPage() {
  const stats = [
    {
      title: "总收入",
      value: "¥45,231.89",
      change: "+20.1% 较上月",
      icon: <TrendingUp className="h-8 w-8" />,
      trend: "up" as const,
    },
    {
      title: "用户数量",
      value: "+2350",
      change: "+180.1% 较上月",
      icon: <Users className="h-8 w-8" />,
      trend: "up" as const,
    },
    {
      title: "销售额",
      value: "+12,234",
      change: "+19% 较上月",
      icon: <ShoppingCart className="h-8 w-8" />,
      trend: "up" as const,
    },
    {
      title: "活跃用户",
      value: "+573",
      change: "+201 较昨日",
      icon: <BarChart3 className="h-8 w-8" />,
      trend: "up" as const,
    },
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">仪表板</h1>
        <p className="text-muted-foreground">
          欢迎回来！这里是您的业务概览。
        </p>
      </div>

      {/* 统计卡片网格 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      {/* 图表区域 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <div className="col-span-4">
          <div className="bg-card rounded-lg border p-6">
            <h3 className="text-lg font-semibold mb-4">收入概览</h3>
            <div className="h-[300px] flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <BarChart3 className="h-16 w-16 mx-auto mb-4" />
                <p>图表组件占位符</p>
                <p className="text-sm">可以集成 Chart.js、Recharts 等图表库</p>
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-3">
          <div className="bg-card rounded-lg border p-6">
            <h3 className="text-lg font-semibold mb-4">最近活动</h3>
            <div className="space-y-4">
              {[
                { user: "张三", action: "创建了新订单", time: "2分钟前" },
                { user: "李四", action: "更新了用户资料", time: "5分钟前" },
                { user: "王五", action: "完成了支付", time: "10分钟前" },
                { user: "赵六", action: "注册了新账户", time: "15分钟前" },
              ].map((activity, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-primary">
                      {activity.user.charAt(0)}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">{activity.user}</p>
                    <p className="text-xs text-muted-foreground">{activity.action}</p>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {activity.time}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
