import React from "react"
import { Users, UserPlus, Search, Filter } from "lucide-react"
import { Button } from "@/components/ui/button"

/**
 * 用户列表页面
 */
export default function UsersListPage() {
  const users = [
    {
      id: 1,
      name: "张三",
      email: "z<PERSON><PERSON>@example.com",
      role: "管理员",
      status: "活跃",
      lastLogin: "2024-01-15 10:30",
    },
    {
      id: 2,
      name: "李四",
      email: "<EMAIL>",
      role: "编辑",
      status: "活跃",
      lastLogin: "2024-01-15 09:15",
    },
    {
      id: 3,
      name: "王五",
      email: "<EMAIL>",
      role: "用户",
      status: "离线",
      lastLogin: "2024-01-14 16:45",
    },
    {
      id: 4,
      name: "赵六",
      email: "zhao<PERSON><EMAIL>",
      role: "用户",
      status: "活跃",
      lastLogin: "2024-01-15 11:20",
    },
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">用户管理</h1>
          <p className="text-muted-foreground">
            管理系统用户和权限设置。
          </p>
        </div>
        <Button className="flex items-center space-x-2">
          <UserPlus className="h-4 w-4" />
          <span>添加用户</span>
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="bg-card rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">总用户数</p>
              <p className="text-2xl font-bold">1,234</p>
            </div>
            <Users className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">活跃用户</p>
              <p className="text-2xl font-bold">987</p>
            </div>
            <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
              <div className="h-3 w-3 bg-green-500 rounded-full"></div>
            </div>
          </div>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">新用户</p>
              <p className="text-2xl font-bold">45</p>
              <p className="text-xs text-green-600">本月新增</p>
            </div>
            <UserPlus className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">管理员</p>
              <p className="text-2xl font-bold">12</p>
            </div>
            <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-xs font-bold">A</span>
            </div>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="搜索用户..."
            className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring"
          />
        </div>
        <Button variant="outline" className="flex items-center space-x-2">
          <Filter className="h-4 w-4" />
          <span>筛选</span>
        </Button>
      </div>

      {/* 用户表格 */}
      <div className="bg-card rounded-lg border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left p-4 font-medium">用户</th>
                <th className="text-left p-4 font-medium">角色</th>
                <th className="text-left p-4 font-medium">状态</th>
                <th className="text-left p-4 font-medium">最后登录</th>
                <th className="text-left p-4 font-medium">操作</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id} className="border-b last:border-b-0 hover:bg-muted/50">
                  <td className="p-4">
                    <div>
                      <div className="font-medium">{user.name}</div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                    </div>
                  </td>
                  <td className="p-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      user.role === "管理员" 
                        ? "bg-blue-100 text-blue-800" 
                        : user.role === "编辑"
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}>
                      {user.role}
                    </span>
                  </td>
                  <td className="p-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      user.status === "活跃" 
                        ? "bg-green-100 text-green-800" 
                        : "bg-gray-100 text-gray-800"
                    }`}>
                      {user.status}
                    </span>
                  </td>
                  <td className="p-4 text-sm text-muted-foreground">
                    {user.lastLogin}
                  </td>
                  <td className="p-4">
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm">
                        编辑
                      </Button>
                      <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                        删除
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
