"use client"

import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, BarChart3, Users, Settings } from "lucide-react"

import { cn } from "@/lib/utils"

/**
 * 移动端底部导航项
 */
interface MobileNavItem {
  id: string
  title: string
  icon: React.ComponentType<{ className?: string }>
  href: string
}

/**
 * 移动端导航配置
 */
const mobileNavItems: MobileNavItem[] = [
  {
    id: "dashboard",
    title: "首页",
    icon: Home,
    href: "/dashboard",
  },
  {
    id: "analytics",
    title: "分析",
    icon: BarChart3,
    href: "/analytics/overview",
  },
  {
    id: "users",
    title: "用户",
    icon: Users,
    href: "/users/list",
  },
  {
    id: "settings",
    title: "设置",
    icon: Settings,
    href: "/settings/general",
  },
]

interface MobileNavProps {
  /** 自定义类名 */
  className?: string
}

/**
 * 移动端底部导航组件
 * 在小屏幕设备上显示，提供快速访问主要功能的入口
 */
export const MobileNav: React.FC<MobileNavProps> = ({ className }) => {
  const pathname = usePathname()

  return (
    <nav
      className={cn(
        "fixed bottom-0 left-0 right-0 z-50 bg-background border-t lg:hidden",
        className
      )}
    >
      <div className="flex items-center justify-around py-2">
        {mobileNavItems.map((item) => {
          const isActive = pathname.startsWith(item.href)
          const Icon = item.icon

          return (
            <Link
              key={item.id}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1 text-xs transition-colors",
                isActive
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              )}
            >
              <Icon
                className={cn(
                  "h-5 w-5 mb-1",
                  isActive && "text-primary"
                )}
              />
              <span className="truncate">{item.title}</span>
            </Link>
          )
        })}
      </div>
    </nav>
  )
}
