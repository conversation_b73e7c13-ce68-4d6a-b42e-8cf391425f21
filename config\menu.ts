import {
  Home,
  BarChart3,
  Users,
  Settings,
  FileText,
  ShoppingCart,
  Package,
  TrendingUp,
  UserCheck,
  Shield,
  Bell,
  Palette,
  Database,
  Globe,
  Mail,
  Calendar,
  CreditCard,
  Truck,
  Star,
  MessageSquare,
  HelpCircle,
  BookOpen,
  Zap,
  Target,
} from "lucide-react"
import { MenuItem } from "@/types/menu"

/**
 * 仪表板菜单配置
 * 支持三级嵌套菜单结构
 */
export const menuConfig: MenuItem[] = [
  {
    id: "dashboard",
    title: "仪表板",
    icon: Home,
    href: "/dashboard",
  },
  {
    id: "analytics",
    title: "数据分析",
    icon: BarChart3,
    children: [
      {
        id: "analytics-overview",
        title: "概览",
        icon: TrendingUp,
        href: "/analytics/overview",
      },
      {
        id: "analytics-reports",
        title: "报告",
        icon: FileText,
        children: [
          {
            id: "analytics-reports-sales",
            title: "销售报告",
            icon: CreditCard,
            href: "/analytics/reports/sales",
          },
          {
            id: "analytics-reports-traffic",
            title: "流量报告",
            icon: Globe,
            href: "/analytics/reports/traffic",
          },
          {
            id: "analytics-reports-performance",
            title: "性能报告",
            icon: Zap,
            href: "/analytics/reports/performance",
          },
        ],
      },
      {
        id: "analytics-metrics",
        title: "指标",
        icon: Target,
        href: "/analytics/metrics",
      },
    ],
  },
  {
    id: "ecommerce",
    title: "电商管理",
    icon: ShoppingCart,
    children: [
      {
        id: "ecommerce-products",
        title: "商品管理",
        icon: Package,
        children: [
          {
            id: "ecommerce-products-list",
            title: "商品列表",
            icon: Package,
            href: "/ecommerce/products/list",
          },
          {
            id: "ecommerce-products-categories",
            title: "分类管理",
            icon: FileText,
            href: "/ecommerce/products/categories",
          },
          {
            id: "ecommerce-products-inventory",
            title: "库存管理",
            icon: Database,
            href: "/ecommerce/products/inventory",
          },
        ],
      },
      {
        id: "ecommerce-orders",
        title: "订单管理",
        icon: Truck,
        href: "/ecommerce/orders",
      },
      {
        id: "ecommerce-customers",
        title: "客户管理",
        icon: Users,
        href: "/ecommerce/customers",
      },
    ],
  },
  {
    id: "users",
    title: "用户管理",
    icon: Users,
    children: [
      {
        id: "users-list",
        title: "用户列表",
        icon: Users,
        href: "/users/list",
      },
      {
        id: "users-roles",
        title: "角色权限",
        icon: Shield,
        href: "/users/roles",
      },
      {
        id: "users-activity",
        title: "用户活动",
        icon: UserCheck,
        href: "/users/activity",
      },
    ],
  },
  {
    id: "communication",
    title: "沟通交流",
    icon: Mail,
    children: [
      {
        id: "communication-messages",
        title: "消息中心",
        icon: MessageSquare,
        href: "/communication/messages",
      },
      {
        id: "communication-notifications",
        title: "通知管理",
        icon: Bell,
        href: "/communication/notifications",
      },
      {
        id: "communication-calendar",
        title: "日程安排",
        icon: Calendar,
        href: "/communication/calendar",
      },
    ],
  },
  {
    id: "settings",
    title: "系统设置",
    icon: Settings,
    children: [
      {
        id: "settings-general",
        title: "常规设置",
        icon: Settings,
        href: "/settings/general",
      },
      {
        id: "settings-appearance",
        title: "外观设置",
        icon: Palette,
        href: "/settings/appearance",
      },
      {
        id: "settings-help",
        title: "帮助中心",
        icon: HelpCircle,
        children: [
          {
            id: "settings-help-docs",
            title: "文档",
            icon: BookOpen,
            href: "/settings/help/docs",
          },
          {
            id: "settings-help-support",
            title: "技术支持",
            icon: MessageSquare,
            href: "/settings/help/support",
          },
          {
            id: "settings-help-feedback",
            title: "意见反馈",
            icon: Star,
            href: "/settings/help/feedback",
          },
        ],
      },
    ],
  },
]
