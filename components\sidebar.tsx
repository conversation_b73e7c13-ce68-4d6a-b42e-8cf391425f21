"use client"

import React, { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronDown, ChevronRight, Menu, X } from "lucide-react"
import { createPortal } from "react-dom"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { MenuItem } from "@/types/menu"
import { menuConfig } from "@/config/menu"

interface SidebarProps {
  /** 是否折叠 */
  isCollapsed: boolean
  /** 折叠状态改变回调 */
  onCollapsedChange: (collapsed: boolean) => void
  /** 自定义类名 */
  className?: string
}

interface FloatingMenuProps {
  /** 菜单项 */
  items: MenuItem[]
  /** 触发元素的位置 */
  triggerRect: DOMRect
  /** 菜单层级 */
  level: number
  /** 关闭回调 */
  onClose: () => void
  /** 当前路径 */
  currentPath: string
}

/**
 * 悬浮菜单组件
 * 支持多级嵌套和平滑动画
 */
const FloatingMenu: React.FC<FloatingMenuProps> = ({
  items,
  triggerRect,
  level,
  onClose,
  currentPath,
}) => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const [childMenuRect, setChildMenuRect] = useState<DOMRect | null>(null)
  const menuRef = useRef<HTMLDivElement>(null)

  // 计算菜单位置
  const menuStyle: React.CSSProperties = {
    position: "fixed",
    left: triggerRect.right + 8,
    top: triggerRect.top,
    zIndex: 1000 + level,
  }

  // 处理鼠标离开
  const handleMouseLeave = () => {
    setTimeout(() => {
      if (!menuRef.current?.matches(":hover")) {
        onClose()
      }
    }, 100)
  }

  return createPortal(
    <div
      ref={menuRef}
      className="floating-menu bg-popover border rounded-md shadow-lg py-1 min-w-[200px]"
      style={menuStyle}
      onMouseLeave={handleMouseLeave}
    >
      {items.map((item) => {
        const isActive = currentPath === item.href
        const hasChildren = item.children && item.children.length > 0
        const isHovered = hoveredItem === item.id

        return (
          <div key={item.id} className="relative">
            {item.href ? (
              <Link
                href={item.href}
                className={cn(
                  "flex items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground transition-colors",
                  isActive && "bg-accent text-accent-foreground font-medium"
                )}
                onClick={onClose}
                onMouseEnter={() => {
                  if (hasChildren) {
                    setHoveredItem(item.id)
                    const rect = menuRef.current?.querySelector(
                      `[data-item-id="${item.id}"]`
                    )?.getBoundingClientRect()
                    if (rect) setChildMenuRect(rect)
                  }
                }}
                data-item-id={item.id}
              >
                <item.icon className="mr-3 h-4 w-4" />
                <span className="flex-1">{item.title}</span>
                {hasChildren && <ChevronRight className="ml-2 h-4 w-4" />}
              </Link>
            ) : (
              <div
                className="flex items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground transition-colors cursor-pointer"
                onMouseEnter={() => {
                  if (hasChildren) {
                    setHoveredItem(item.id)
                    const rect = menuRef.current?.querySelector(
                      `[data-item-id="${item.id}"]`
                    )?.getBoundingClientRect()
                    if (rect) setChildMenuRect(rect)
                  }
                }}
                data-item-id={item.id}
              >
                <item.icon className="mr-3 h-4 w-4" />
                <span className="flex-1">{item.title}</span>
                {hasChildren && <ChevronRight className="ml-2 h-4 w-4" />}
              </div>
            )}

            {/* 子菜单 */}
            {hasChildren && isHovered && childMenuRect && (
              <FloatingMenu
                items={item.children!}
                triggerRect={childMenuRect}
                level={level + 1}
                onClose={onClose}
                currentPath={currentPath}
              />
            )}
          </div>
        )
      })}
    </div>,
    document.body
  )
}

/**
 * 菜单项组件
 */
interface MenuItemComponentProps {
  item: MenuItem
  level: number
  isCollapsed: boolean
  currentPath: string
  expandedItems: string[]
  onToggleExpanded: (itemId: string) => void
}

const MenuItemComponent: React.FC<MenuItemComponentProps> = ({
  item,
  level,
  isCollapsed,
  currentPath,
  expandedItems,
  onToggleExpanded,
}) => {
  const [showFloatingMenu, setShowFloatingMenu] = useState(false)
  const [triggerRect, setTriggerRect] = useState<DOMRect | null>(null)
  const itemRef = useRef<HTMLDivElement>(null)

  const isActive = currentPath === item.href
  const hasChildren = item.children && item.children.length > 0
  const isExpanded = expandedItems.includes(item.id)

  // 处理悬浮菜单显示
  const handleMouseEnter = () => {
    if (isCollapsed && hasChildren && itemRef.current) {
      const rect = itemRef.current.getBoundingClientRect()
      setTriggerRect(rect)
      setShowFloatingMenu(true)
    }
  }

  const handleMouseLeave = () => {
    setTimeout(() => {
      if (!itemRef.current?.matches(":hover")) {
        setShowFloatingMenu(false)
      }
    }, 100)
  }

  const paddingLeft = isCollapsed ? "pl-3" : `pl-${3 + level * 4}`

  return (
    <div ref={itemRef}>
      {item.href ? (
        <Link
          href={item.href}
          className={cn(
            "flex items-center py-2 px-3 text-sm hover:bg-accent hover:text-accent-foreground transition-colors rounded-md mx-2",
            paddingLeft,
            isActive && "bg-accent text-accent-foreground font-medium"
          )}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <item.icon className={cn("h-4 w-4", !isCollapsed && "mr-3")} />
          {!isCollapsed && (
            <>
              <span className="flex-1">{item.title}</span>
              {hasChildren && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 hover:bg-transparent"
                  onClick={(e) => {
                    e.preventDefault()
                    onToggleExpanded(item.id)
                  }}
                >
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              )}
            </>
          )}
        </Link>
      ) : (
        <div
          className={cn(
            "flex items-center py-2 px-3 text-sm hover:bg-accent hover:text-accent-foreground transition-colors rounded-md mx-2 cursor-pointer",
            paddingLeft
          )}
          onClick={() => !isCollapsed && onToggleExpanded(item.id)}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <item.icon className={cn("h-4 w-4", !isCollapsed && "mr-3")} />
          {!isCollapsed && (
            <>
              <span className="flex-1">{item.title}</span>
              {hasChildren && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 hover:bg-transparent"
                >
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              )}
            </>
          )}
        </div>
      )}

      {/* 展开的子菜单 */}
      {!isCollapsed && hasChildren && isExpanded && (
        <div className="mt-1">
          {item.children!.map((child) => (
            <MenuItemComponent
              key={child.id}
              item={child}
              level={level + 1}
              isCollapsed={isCollapsed}
              currentPath={currentPath}
              expandedItems={expandedItems}
              onToggleExpanded={onToggleExpanded}
            />
          ))}
        </div>
      )}

      {/* 悬浮菜单 */}
      {isCollapsed && showFloatingMenu && hasChildren && triggerRect && (
        <FloatingMenu
          items={item.children!}
          triggerRect={triggerRect}
          level={0}
          onClose={() => setShowFloatingMenu(false)}
          currentPath={currentPath}
        />
      )}
    </div>
  )
}

/**
 * 侧边栏组件
 * 支持折叠、三级嵌套菜单、悬浮菜单等功能
 */
export const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  onCollapsedChange,
  className,
}) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const currentPath = usePathname()

  // 切换展开状态
  const handleToggleExpanded = (itemId: string) => {
    setExpandedItems((prev) =>
      prev.includes(itemId)
        ? prev.filter((id) => id !== itemId)
        : [...prev, itemId]
    )
  }

  // 折叠时清空展开状态
  useEffect(() => {
    if (isCollapsed) {
      setExpandedItems([])
    }
  }, [isCollapsed])

  return (
    <div
      className={cn(
        "sidebar-transition bg-card border-r flex flex-col h-full",
        isCollapsed ? "w-16" : "w-64",
        className
      )}
    >
      {/* Logo 区域 */}
      <div className="flex items-center justify-between p-4 border-b">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">D</span>
            </div>
            <span className="font-semibold text-lg">Dashboard</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => onCollapsedChange(!isCollapsed)}
          className="h-8 w-8"
        >
          {isCollapsed ? <Menu className="h-4 w-4" /> : <X className="h-4 w-4" />}
        </Button>
      </div>

      {/* 菜单区域 */}
      <div className="flex-1 overflow-y-auto custom-scrollbar py-4">
        <nav className="space-y-1">
          {menuConfig.map((item) => (
            <MenuItemComponent
              key={item.id}
              item={item}
              level={0}
              isCollapsed={isCollapsed}
              currentPath={currentPath}
              expandedItems={expandedItems}
              onToggleExpanded={handleToggleExpanded}
            />
          ))}
        </nav>
      </div>
    </div>
  )
}
