"use client"

import React, { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { Sidebar } from "@/components/sidebar"
import { Header } from "@/components/header"
import { MobileNav } from "@/components/mobile-nav"

interface DashboardLayoutProps {
  /** 子组件 */
  children: React.ReactNode
  /** 自定义类名 */
  className?: string
}

/**
 * 仪表板布局组件
 * 包含侧边栏、头部和主内容区域
 * 支持响应式设计和侧边栏折叠
 */
export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  className,
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
      // 移动端默认折叠侧边栏
      if (mobile && !sidebarCollapsed) {
        setSidebarCollapsed(true)
      }
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)
    return () => window.removeEventListener("resize", checkMobile)
  }, [sidebarCollapsed])

  // 移动端遮罩层点击处理
  const handleOverlayClick = () => {
    if (isMobile) {
      setSidebarCollapsed(true)
    }
  }

  return (
    <div className={cn("flex h-screen bg-background", className)}>
      {/* 移动端遮罩层 */}
      {isMobile && !sidebarCollapsed && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={handleOverlayClick}
        />
      )}

      {/* 侧边栏 */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0",
          isMobile && sidebarCollapsed && "-translate-x-full",
          isMobile && !sidebarCollapsed && "translate-x-0"
        )}
      >
        <Sidebar
          isCollapsed={sidebarCollapsed}
          onCollapsedChange={setSidebarCollapsed}
        />
      </aside>

      {/* 主内容区域 */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* 头部 */}
        <Header />

        {/* 主内容 */}
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto p-6 pb-20 lg:pb-6">
            {children}
          </div>
        </main>
      </div>

      {/* 移动端底部导航 */}
      <MobileNav />
    </div>
  )
}
