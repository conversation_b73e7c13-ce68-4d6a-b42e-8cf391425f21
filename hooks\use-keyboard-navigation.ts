"use client"

import { useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"

/**
 * 键盘导航配置
 */
interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  action: () => void
  description: string
}

/**
 * 键盘导航 Hook
 * 提供全局键盘快捷键支持
 */
export const useKeyboardNavigation = () => {
  const router = useRouter()

  // 定义快捷键
  const shortcuts: KeyboardShortcut[] = [
    {
      key: "h",
      altKey: true,
      action: () => router.push("/dashboard"),
      description: "跳转到首页",
    },
    {
      key: "a",
      altKey: true,
      action: () => router.push("/analytics/overview"),
      description: "跳转到数据分析",
    },
    {
      key: "u",
      altKey: true,
      action: () => router.push("/users/list"),
      description: "跳转到用户管理",
    },
    {
      key: "s",
      altKey: true,
      action: () => router.push("/settings/general"),
      description: "跳转到设置",
    },
    {
      key: "/",
      action: () => {
        // 聚焦到搜索框（如果存在）
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="搜索"]') as HTMLInputElement
        if (searchInput) {
          searchInput.focus()
        }
      },
      description: "聚焦搜索框",
    },
    {
      key: "Escape",
      action: () => {
        // 移除焦点
        const activeElement = document.activeElement as HTMLElement
        if (activeElement && activeElement.blur) {
          activeElement.blur()
        }
      },
      description: "取消焦点",
    },
  ]

  // 处理键盘事件
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // 如果用户正在输入，不处理快捷键
    const target = event.target as HTMLElement
    if (
      target.tagName === "INPUT" ||
      target.tagName === "TEXTAREA" ||
      target.contentEditable === "true"
    ) {
      // 只处理 Escape 键
      if (event.key === "Escape") {
        const escapeShortcut = shortcuts.find(s => s.key === "Escape")
        if (escapeShortcut) {
          event.preventDefault()
          escapeShortcut.action()
        }
      }
      return
    }

    // 查找匹配的快捷键
    const matchedShortcut = shortcuts.find(
      (shortcut) =>
        shortcut.key === event.key &&
        !!shortcut.ctrlKey === event.ctrlKey &&
        !!shortcut.altKey === event.altKey &&
        !!shortcut.shiftKey === event.shiftKey
    )

    if (matchedShortcut) {
      event.preventDefault()
      matchedShortcut.action()
    }
  }, [router, shortcuts])

  // 注册键盘事件监听器
  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown)
    return () => {
      document.removeEventListener("keydown", handleKeyDown)
    }
  }, [handleKeyDown])

  return {
    shortcuts,
  }
}

/**
 * 焦点管理 Hook
 * 提供焦点陷阱和焦点管理功能
 */
export const useFocusManagement = () => {
  /**
   * 创建焦点陷阱
   * @param container 容器元素
   */
  const createFocusTrap = useCallback((container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== "Tab") return

      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault()
          lastElement.focus()
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault()
          firstElement.focus()
        }
      }
    }

    container.addEventListener("keydown", handleTabKey)

    // 返回清理函数
    return () => {
      container.removeEventListener("keydown", handleTabKey)
    }
  }, [])

  /**
   * 设置焦点到第一个可聚焦元素
   * @param container 容器元素
   */
  const focusFirstElement = useCallback((container: HTMLElement) => {
    const firstFocusable = container.querySelector(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as HTMLElement

    if (firstFocusable) {
      firstFocusable.focus()
    }
  }, [])

  return {
    createFocusTrap,
    focusFirstElement,
  }
}
