import React from "react"
import { TrendingUp, <PERSON><PERSON><PERSON><PERSON>, Pie<PERSON><PERSON>, Activity } from "lucide-react"

/**
 * 数据分析概览页面
 */
export default function AnalyticsOverviewPage() {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">数据分析概览</h1>
        <p className="text-muted-foreground">
          查看您的业务数据分析和趋势报告。
        </p>
      </div>

      {/* 关键指标 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="bg-card rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">页面浏览量</p>
              <p className="text-2xl font-bold">124,567</p>
              <p className="text-xs text-green-600">+12.5% 较上周</p>
            </div>
            <TrendingUp className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">独立访客</p>
              <p className="text-2xl font-bold">23,456</p>
              <p className="text-xs text-green-600">+8.2% 较上周</p>
            </div>
            <Activity className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">转化率</p>
              <p className="text-2xl font-bold">3.24%</p>
              <p className="text-xs text-red-600">-0.5% 较上周</p>
            </div>
            <PieChart className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">平均停留时间</p>
              <p className="text-2xl font-bold">2:34</p>
              <p className="text-xs text-green-600">+15s 较上周</p>
            </div>
            <BarChart3 className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="grid gap-4 lg:grid-cols-2">
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">流量趋势</h3>
          <div className="h-[300px] flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <TrendingUp className="h-16 w-16 mx-auto mb-4" />
              <p>流量趋势图表</p>
              <p className="text-sm">显示过去30天的访问量变化</p>
            </div>
          </div>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">用户来源</h3>
          <div className="h-[300px] flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <PieChart className="h-16 w-16 mx-auto mb-4" />
              <p>用户来源饼图</p>
              <p className="text-sm">显示不同渠道的用户占比</p>
            </div>
          </div>
        </div>
      </div>

      {/* 详细数据表格 */}
      <div className="bg-card rounded-lg border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold">热门页面</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {[
              { page: "/dashboard", views: "12,345", rate: "45.2%" },
              { page: "/analytics", views: "8,901", rate: "32.1%" },
              { page: "/users", views: "5,678", rate: "20.5%" },
              { page: "/settings", views: "2,345", rate: "8.9%" },
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between py-2">
                <div className="font-medium">{item.page}</div>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <span>{item.views} 次浏览</span>
                  <span>{item.rate} 占比</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
