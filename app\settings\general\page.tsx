import React from "react"
import { Save, Settings } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"

/**
 * 常规设置页面
 */
export default function GeneralSettingsPage() {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">常规设置</h1>
        <p className="text-muted-foreground">
          管理您的账户设置和偏好。
        </p>
      </div>

      {/* 设置表单 */}
      <div className="max-w-2xl space-y-8">
        {/* 个人信息 */}
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Settings className="mr-2 h-5 w-5" />
            个人信息
          </h3>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">姓名</label>
                <input
                  type="text"
                  defaultValue="演示用户"
                  className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">用户名</label>
                <input
                  type="text"
                  defaultValue="demo_user"
                  className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">邮箱地址</label>
              <input
                type="email"
                defaultValue="<EMAIL>"
                className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">个人简介</label>
              <textarea
                rows={3}
                defaultValue="这是一个演示用户账户，用于展示仪表板功能。"
                className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring resize-none"
              />
            </div>
          </div>
        </div>

        {/* 通知设置 */}
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">通知设置</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">邮件通知</p>
                <p className="text-sm text-muted-foreground">接收重要更新的邮件通知</p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">推送通知</p>
                <p className="text-sm text-muted-foreground">接收浏览器推送通知</p>
              </div>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">短信通知</p>
                <p className="text-sm text-muted-foreground">接收重要安全提醒的短信</p>
              </div>
              <Switch defaultChecked />
            </div>
          </div>
        </div>

        {/* 隐私设置 */}
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">隐私设置</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">公开个人资料</p>
                <p className="text-sm text-muted-foreground">允许其他用户查看您的个人资料</p>
              </div>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">显示在线状态</p>
                <p className="text-sm text-muted-foreground">向其他用户显示您的在线状态</p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">数据分析</p>
                <p className="text-sm text-muted-foreground">允许收集使用数据以改善服务</p>
              </div>
              <Switch defaultChecked />
            </div>
          </div>
        </div>

        {/* 语言和地区 */}
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">语言和地区</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">语言</label>
              <select className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring">
                <option value="zh-CN">简体中文</option>
                <option value="en-US">English (US)</option>
                <option value="ja-JP">日本語</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">时区</label>
              <select className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring">
                <option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</option>
                <option value="America/New_York">America/New_York (UTC-5)</option>
                <option value="Europe/London">Europe/London (UTC+0)</option>
              </select>
            </div>
          </div>
        </div>

        {/* 保存按钮 */}
        <div className="flex justify-end">
          <Button className="flex items-center space-x-2">
            <Save className="h-4 w-4" />
            <span>保存设置</span>
          </Button>
        </div>
      </div>
    </div>
  )
}
