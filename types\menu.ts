import { LucideIcon } from "lucide-react"

/**
 * 菜单项接口定义
 */
export interface MenuItem {
  /** 菜单项唯一标识 */
  id: string
  /** 菜单项标题 */
  title: string
  /** 菜单项图标 */
  icon: LucideIcon
  /** 路由路径 */
  href?: string
  /** 子菜单项 */
  children?: MenuItem[]
  /** 是否禁用 */
  disabled?: boolean
  /** 菜单项描述 */
  description?: string
}

/**
 * 侧边栏状态接口
 */
export interface SidebarState {
  /** 是否折叠 */
  isCollapsed: boolean
  /** 当前激活的菜单项 */
  activeItem: string | null
  /** 展开的菜单项 */
  expandedItems: string[]
}

/**
 * 面包屑项接口
 */
export interface BreadcrumbItem {
  /** 标题 */
  title: string
  /** 路径 */
  href?: string
  /** 是否为当前页面 */
  isCurrentPage?: boolean
}
