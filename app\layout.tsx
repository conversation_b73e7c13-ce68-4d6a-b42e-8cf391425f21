import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Dashboard Scaffold",
  description: "A lightweight, reusable dashboard scaffold built with Next.js and Tailwind CSS",
}

/**
 * 根布局组件
 * 提供全局样式和主题支持
 */
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          defaultTheme="system"
          storageKey="dashboard-theme"
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
